'use client';

import { m } from 'framer-motion';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { varAlpha } from 'minimal-shared/utils';

import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

const PROCESS_STEPS = [
  {
    step: '01',
    title: 'Chọn sản phẩm',
    description: 'Duyệt qua catalog túi vải đa dạng, từ canvas đến cotton premium',
    icon: 'solar:bag-4-bold',
    color: 'primary',
  },
  {
    step: '02', 
    title: 'Upload logo',
    description: 'Tải lên logo và sử dụng mockup tool để xem trước kết quả',
    icon: 'solar:upload-bold',
    color: 'secondary',
  },
  {
    step: '03',
    title: 'Báo giá tự động',
    description: 'Nhận báo giá chính xác ngay lập tức dựa trên specs và số lượng',
    icon: 'solar:calculator-bold',
    color: 'success',
  },
  {
    step: '04',
    title: 'Sản xuất & giao hàng',
    description: 'Quy trình sản xuất 6 bước đảm bảo chất lượng đồng nhất',
    icon: 'solar:delivery-bold',
    color: 'warning',
  },
];

// ----------------------------------------------------------------------

export function LomaProcess() {
  const theme = useTheme();

  const renderHeader = () => (
    <Stack spacing={3} sx={{ textAlign: 'center', mb: 8 }}>
      <m.div variants={varFade().inUp}>
        <Typography variant="overline" sx={{ color: 'primary.main', fontWeight: 700 }}>
          QUY TRÌNH ĐƠN GIẢN
        </Typography>
      </m.div>
      
      <m.div variants={varFade().inUp}>
        <Typography variant="h2" sx={{ fontWeight: 700 }}>
          Chỉ 4 bước để có túi vải
          <br />
          <Box component="span" sx={{ color: 'primary.main' }}>
            hoàn hảo
          </Box>
        </Typography>
      </m.div>
      
      <m.div variants={varFade().inUp}>
        <Typography variant="h6" sx={{ color: 'text.secondary', maxWidth: 600, mx: 'auto' }}>
          Từ ý tưởng đến sản phẩm hoàn thiện, chúng tôi đơn giản hóa mọi thứ 
          để bạn tập trung vào việc phát triển thương hiệu.
        </Typography>
      </m.div>
    </Stack>
  );

  const renderSteps = () => (
    <Box
      sx={{
        display: 'grid',
        gap: 4,
        gridTemplateColumns: {
          xs: 'repeat(1, 1fr)',
          sm: 'repeat(2, 1fr)',
          md: 'repeat(4, 1fr)',
        },
      }}
    >
      {PROCESS_STEPS.map((step, index) => (
        <m.div key={step.step} variants={varFade().inUp}>
          <Card
            sx={{
              p: 4,
              height: 1,
              textAlign: 'center',
              position: 'relative',
              border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.12)}`,
              transition: 'all 0.3s ease',
              '&:hover': {
                boxShadow: `0 12px 40px ${alpha(theme.palette[step.color].main, 0.12)}`,
                transform: 'translateY(-8px)',
                borderColor: `${step.color}.main`,
              },
            }}
          >
            {/* Step Number */}
            <Box
              sx={{
                top: -16,
                left: 24,
                width: 32,
                height: 32,
                borderRadius: '50%',
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: `${step.color}.main`,
                color: 'common.white',
                fontWeight: 700,
                fontSize: 14,
              }}
            >
              {step.step}
            </Box>

            {/* Icon */}
            <Box
              sx={{
                mb: 3,
                width: 64,
                height: 64,
                borderRadius: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: varAlpha(theme.vars.palette[step.color].mainChannel, 0.1),
                mx: 'auto',
              }}
            >
              <Iconify
                icon={step.icon}
                sx={{
                  width: 32,
                  height: 32,
                  color: `${step.color}.main`,
                }}
              />
            </Box>

            {/* Content */}
            <Stack spacing={2}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {step.title}
              </Typography>
              
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {step.description}
              </Typography>
            </Stack>

            {/* Connector Line */}
            {index < PROCESS_STEPS.length - 1 && (
              <Box
                sx={{
                  top: 80,
                  right: -32,
                  width: 64,
                  height: 2,
                  position: 'absolute',
                  background: `linear-gradient(90deg, ${theme.palette[step.color].main}, ${theme.palette[PROCESS_STEPS[index + 1].color].main})`,
                  display: { xs: 'none', md: 'block' },
                  '&::after': {
                    top: -4,
                    right: -4,
                    width: 10,
                    height: 10,
                    content: '""',
                    position: 'absolute',
                    borderRadius: '50%',
                    bgcolor: theme.palette[PROCESS_STEPS[index + 1].color].main,
                  },
                }}
              />
            )}
          </Card>
        </m.div>
      ))}
    </Box>
  );

  return (
    <Container
      component={MotionViewport}
      sx={{
        py: { xs: 8, md: 12 },
        position: 'relative',
      }}
    >
      {renderHeader()}
      {renderSteps()}
    </Container>
  );
}
