'use client';

import { m } from 'framer-motion';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Rating from '@mui/material/Rating';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { varAlpha } from 'minimal-shared/utils';

import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

const TESTIMONIALS = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    role: 'CEO, Fashion Store Online',
    avatar: '/assets/images/avatars/avatar-1.jpg',
    rating: 5,
    content: 'Chất lượng túi vải vượt ngoài mong đợi! Logo in rất sắc nét và bền màu. Khách hàng của tôi rất thích và thường xuyên hỏi mua thêm.',
    highlight: 'Chất lượng vượt ngoài mong đợi',
  },
  {
    id: 2,
    name: 'Trần Văn Hùng',
    role: 'Marketing Manager, Tech Startup',
    avatar: '/assets/images/avatars/avatar-2.jpg',
    rating: 5,
    content: 'Quy trình báo giá tự động rất tiện lợi. Từ mockup đến nhận hàng chỉ mất 5 ngày. Đội ngũ hỗ trợ nhiệt tình và chuyên nghiệp.',
    highlight: 'Quy trình nhanh chóng và chuyên nghiệp',
  },
  {
    id: 3,
    name: 'Lê Thị Mai',
    role: 'Founder, Eco Brand',
    avatar: '/assets/images/avatars/avatar-3.jpg',
    rating: 5,
    content: 'Tôi đặc biệt ấn tượng với chất liệu cotton organic. Phù hợp hoàn hảo với thương hiệu xanh của chúng tôi. Sẽ tiếp tục hợp tác lâu dài.',
    highlight: 'Chất liệu thân thiện môi trường',
  },
];

// ----------------------------------------------------------------------

export function LomaTestimonials() {
  const theme = useTheme();

  const renderHeader = () => (
    <Stack spacing={3} sx={{ textAlign: 'center', mb: 8 }}>
      <m.div variants={varFade().inUp}>
        <Typography variant="overline" sx={{ color: 'primary.main', fontWeight: 700 }}>
          KHÁCH HÀNG NÓI GÌ
        </Typography>
      </m.div>
      
      <m.div variants={varFade().inUp}>
        <Typography variant="h2" sx={{ fontWeight: 700 }}>
          Hơn 500 khách hàng
          <br />
          <Box component="span" sx={{ color: 'primary.main' }}>
            tin tưởng Loma
          </Box>
        </Typography>
      </m.div>
      
      <m.div variants={varFade().inUp}>
        <Typography variant="h6" sx={{ color: 'text.secondary', maxWidth: 600, mx: 'auto' }}>
          Từ startup đến doanh nghiệp lớn, chúng tôi tự hào được đồng hành 
          cùng các thương hiệu trong hành trình phát triển.
        </Typography>
      </m.div>
    </Stack>
  );

  const renderTestimonials = () => (
    <Box
      sx={{
        display: 'grid',
        gap: 4,
        gridTemplateColumns: {
          xs: 'repeat(1, 1fr)',
          md: 'repeat(3, 1fr)',
        },
      }}
    >
      {TESTIMONIALS.map((testimonial, index) => (
        <m.div key={testimonial.id} variants={varFade().inUp}>
          <Card
            sx={{
              p: 4,
              height: 1,
              position: 'relative',
              border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.12)}`,
              transition: 'all 0.3s ease',
              '&:hover': {
                boxShadow: `0 20px 60px ${alpha(theme.palette.primary.main, 0.08)}`,
                transform: 'translateY(-8px)',
              },
            }}
          >
            {/* Quote Icon */}
            <Box
              sx={{
                top: -16,
                right: 24,
                width: 32,
                height: 32,
                borderRadius: '50%',
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: 'primary.main',
                color: 'common.white',
              }}
            >
              <Iconify icon="solar:quote-up-bold" sx={{ width: 16, height: 16 }} />
            </Box>

            <Stack spacing={3} sx={{ height: 1 }}>
              {/* Rating */}
              <Rating value={testimonial.rating} readOnly size="small" />

              {/* Highlight */}
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: 'primary.main',
                  fontSize: 16,
                }}
              >
                "{testimonial.highlight}"
              </Typography>

              {/* Content */}
              <Typography
                variant="body2"
                sx={{
                  color: 'text.secondary',
                  lineHeight: 1.6,
                  flexGrow: 1,
                }}
              >
                {testimonial.content}
              </Typography>

              {/* Author */}
              <Stack direction="row" spacing={2} alignItems="center">
                <Avatar
                  sx={{
                    width: 48,
                    height: 48,
                    bgcolor: varAlpha(theme.vars.palette.primary.mainChannel, 0.1),
                  }}
                >
                  <Typography variant="h6" sx={{ color: 'primary.main' }}>
                    {testimonial.name.charAt(0)}
                  </Typography>
                </Avatar>
                
                <Stack spacing={0.5}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    {testimonial.name}
                  </Typography>
                  <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                    {testimonial.role}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>
          </Card>
        </m.div>
      ))}
    </Box>
  );

  const renderStats = () => (
    <m.div variants={varFade().inUp}>
      <Box
        sx={{
          mt: 8,
          p: 4,
          borderRadius: 3,
          textAlign: 'center',
          background: `linear-gradient(135deg, ${varAlpha(theme.vars.palette.primary.mainChannel, 0.08)} 0%, ${varAlpha(theme.vars.palette.secondary.mainChannel, 0.08)} 100%)`,
          border: `1px solid ${varAlpha(theme.vars.palette.primary.mainChannel, 0.12)}`,
        }}
      >
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={4}
          divider={
            <Box
              sx={{
                width: 1,
                height: 40,
                bgcolor: 'divider',
                display: { xs: 'none', sm: 'block' },
              }}
            />
          }
        >
          {[
            { number: '4.9/5', label: 'Đánh giá trung bình' },
            { number: '99%', label: 'Khách hàng hài lòng' },
            { number: '48h', label: 'Thời gian phản hồi' },
            { number: '500+', label: 'Dự án hoàn thành' },
          ].map((stat, index) => (
            <Stack key={index} spacing={1} alignItems="center" sx={{ flex: 1 }}>
              <Typography variant="h3" sx={{ fontWeight: 700, color: 'primary.main' }}>
                {stat.number}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {stat.label}
              </Typography>
            </Stack>
          ))}
        </Stack>
      </Box>
    </m.div>
  );

  return (
    <Container
      component={MotionViewport}
      sx={{
        py: { xs: 8, md: 12 },
      }}
    >
      {renderHeader()}
      {renderTestimonials()}
      {renderStats()}
    </Container>
  );
}
