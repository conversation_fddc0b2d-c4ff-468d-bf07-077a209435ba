'use client';

import { m } from 'framer-motion';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';
import { alpha, useTheme } from '@mui/material/styles';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { varAlpha } from 'minimal-shared/utils';

import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

const FEATURED_PRODUCTS = [
  {
    id: 1,
    name: 'Túi Canvas Premium',
    description: 'Chất liệu canvas dày dặn, bền bỉ. <PERSON><PERSON> hợp cho thương hiệu cao cấp.',
    image: '/assets/images/products/canvas-bag.jpg',
    features: ['Canvas 16oz', 'Kích thước đa dạng', 'In silk screen'],
    price: 'Từ 25,000đ',
    popular: true,
  },
  {
    id: 2,
    name: 'Túi Cotton Organic',
    description: 'Cotton hữu cơ thân thiện môi trường, mềm mại và thoáng khí.',
    image: '/assets/images/products/cotton-bag.jpg',
    features: ['Cotton 100%', 'Eco-friendly', 'In transfer'],
    price: 'Từ 18,000đ',
    popular: false,
  },
  {
    id: 3,
    name: 'Túi Không Dệt',
    description: 'Giải pháp kinh tế, nhẹ và tiện dụng cho các chiến dịch marketing.',
    image: '/assets/images/products/nonwoven-bag.jpg',
    features: ['Non-woven 80gsm', 'Nhiều màu sắc', 'In offset'],
    price: 'Từ 8,000đ',
    popular: false,
  },
];

// ----------------------------------------------------------------------

export function LomaProducts() {
  const theme = useTheme();

  const renderHeader = () => (
    <Stack spacing={3} sx={{ textAlign: 'center', mb: 8 }}>
      <m.div variants={varFade().inUp}>
        <Typography variant="overline" sx={{ color: 'primary.main', fontWeight: 700 }}>
          SẢN PHẨM NỔI BẬT
        </Typography>
      </m.div>
      
      <m.div variants={varFade().inUp}>
        <Typography variant="h2" sx={{ fontWeight: 700 }}>
          Chọn túi vải phù hợp
          <br />
          <Box component="span" sx={{ color: 'primary.main' }}>
            thương hiệu của bạn
          </Box>
        </Typography>
      </m.div>
      
      <m.div variants={varFade().inUp}>
        <Typography variant="h6" sx={{ color: 'text.secondary', maxWidth: 600, mx: 'auto' }}>
          Từ canvas premium đến cotton hữu cơ, chúng tôi có đầy đủ các loại túi vải 
          để đáp ứng mọi nhu cầu và ngân sách.
        </Typography>
      </m.div>
    </Stack>
  );

  const renderProducts = () => (
    <Box
      sx={{
        display: 'grid',
        gap: 4,
        gridTemplateColumns: {
          xs: 'repeat(1, 1fr)',
          md: 'repeat(3, 1fr)',
        },
      }}
    >
      {FEATURED_PRODUCTS.map((product, index) => (
        <m.div key={product.id} variants={varFade().inUp}>
          <Card
            sx={{
              height: 1,
              position: 'relative',
              border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.12)}`,
              transition: 'all 0.3s ease',
              '&:hover': {
                boxShadow: `0 20px 60px ${alpha(theme.palette.primary.main, 0.12)}`,
                transform: 'translateY(-8px)',
              },
            }}
          >
            {/* Popular Badge */}
            {product.popular && (
              <Chip
                label="Phổ biến nhất"
                color="primary"
                size="small"
                sx={{
                  position: 'absolute',
                  top: 16,
                  right: 16,
                  zIndex: 2,
                  fontWeight: 600,
                }}
              />
            )}

            {/* Product Image */}
            <Box
              sx={{
                height: 240,
                position: 'relative',
                bgcolor: 'grey.100',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
              }}
            >
              {/* Placeholder for product image */}
              <Box
                sx={{
                  width: 120,
                  height: 120,
                  borderRadius: 2,
                  bgcolor: varAlpha(theme.vars.palette.primary.mainChannel, 0.1),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Iconify
                  icon="solar:bag-4-bold"
                  sx={{
                    width: 60,
                    height: 60,
                    color: 'primary.main',
                  }}
                />
              </Box>
            </Box>

            <CardContent sx={{ p: 3 }}>
              <Stack spacing={2}>
                {/* Product Name & Price */}
                <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {product.name}
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 700 }}>
                    {product.price}
                  </Typography>
                </Stack>

                {/* Description */}
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {product.description}
                </Typography>

                {/* Features */}
                <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                  {product.features.map((feature) => (
                    <Chip
                      key={feature}
                      label={feature}
                      size="small"
                      variant="outlined"
                      sx={{
                        borderColor: 'primary.main',
                        color: 'primary.main',
                        fontSize: 11,
                      }}
                    />
                  ))}
                </Stack>

                {/* CTA Button */}
                <Button
                  component={RouterLink}
                  href={`${paths.products}/${product.id}`}
                  variant="outlined"
                  fullWidth
                  startIcon={<Iconify icon="solar:eye-bold" />}
                  sx={{
                    mt: 2,
                    borderRadius: 1.5,
                    '&:hover': {
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  Xem chi tiết
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </m.div>
      ))}
    </Box>
  );

  const renderCTA = () => (
    <m.div variants={varFade().inUp}>
      <Stack spacing={3} alignItems="center" sx={{ mt: 8, textAlign: 'center' }}>
        <Typography variant="h5" sx={{ fontWeight: 600 }}>
          Không tìm thấy sản phẩm phù hợp?
        </Typography>
        
        <Typography variant="body1" sx={{ color: 'text.secondary', maxWidth: 500 }}>
          Chúng tôi có thể tùy chỉnh kích thước, màu sắc và chất liệu theo yêu cầu riêng của bạn.
        </Typography>
        
        <Button
          component={RouterLink}
          href={paths.products}
          variant="contained"
          size="large"
          startIcon={<Iconify icon="solar:gallery-bold" />}
          sx={{
            py: 1.5,
            px: 4,
            borderRadius: 2,
            fontWeight: 600,
          }}
        >
          Xem tất cả sản phẩm
        </Button>
      </Stack>
    </m.div>
  );

  return (
    <Box sx={{ bgcolor: 'grey.50', py: { xs: 8, md: 12 } }}>
      <Container component={MotionViewport}>
        {renderHeader()}
        {renderProducts()}
        {renderCTA()}
      </Container>
    </Box>
  );
}
