'use client';

import { m } from 'framer-motion';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { varAlpha } from 'minimal-shared/utils';

import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

export function LomaCTA() {
  const theme = useTheme();

  return (
    <Box
      sx={{
        py: { xs: 8, md: 12 },
        position: 'relative',
        overflow: 'hidden',
        background: `linear-gradient(135deg, 
          ${theme.vars.palette.primary.main} 0%, 
          ${theme.vars.palette.primary.dark} 50%,
          ${theme.vars.palette.secondary.main} 100%)`,
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          position: 'absolute',
          opacity: 0.1,
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}
      />

      <Container
        component={MotionViewport}
        sx={{
          position: 'relative',
          zIndex: 2,
        }}
      >
        <Stack
          spacing={5}
          alignItems="center"
          sx={{
            textAlign: 'center',
            color: 'common.white',
            maxWidth: 800,
            mx: 'auto',
          }}
        >
          {/* Main Content */}
          <Stack spacing={3}>
            <m.div variants={varFade().inUp}>
              <Typography
                variant="h2"
                sx={{
                  fontWeight: 700,
                  fontSize: { xs: 32, md: 48 },
                  lineHeight: 1.2,
                }}
              >
                Sẵn sàng tạo ra túi vải
                <br />
                hoàn hảo cho thương hiệu?
              </Typography>
            </m.div>

            <m.div variants={varFade().inUp}>
              <Typography
                variant="h6"
                sx={{
                  opacity: 0.9,
                  fontWeight: 400,
                  maxWidth: 600,
                  mx: 'auto',
                  lineHeight: 1.6,
                }}
              >
                Bắt đầu với mockup miễn phí hoặc nhận báo giá tự động ngay hôm nay. 
                Đội ngũ chuyên gia của chúng tôi sẵn sàng hỗ trợ bạn 24/7.
              </Typography>
            </m.div>
          </Stack>

          {/* CTA Buttons */}
          <m.div variants={varFade().inUp}>
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={3}
              justifyContent="center"
            >
              <Button
                component={RouterLink}
                href={paths.mockup}
                variant="contained"
                size="large"
                startIcon={<Iconify icon="solar:palette-2-bold" />}
                sx={{
                  py: 2,
                  px: 4,
                  fontSize: 16,
                  fontWeight: 600,
                  borderRadius: 2,
                  bgcolor: 'common.white',
                  color: 'primary.main',
                  boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.2)}`,
                  '&:hover': {
                    bgcolor: 'grey.100',
                    boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.3)}`,
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Tạo mockup miễn phí
              </Button>

              <Button
                component={RouterLink}
                href={paths.quote}
                variant="outlined"
                size="large"
                startIcon={<Iconify icon="solar:calculator-bold" />}
                sx={{
                  py: 2,
                  px: 4,
                  fontSize: 16,
                  fontWeight: 600,
                  borderRadius: 2,
                  borderColor: 'common.white',
                  color: 'common.white',
                  borderWidth: 2,
                  '&:hover': {
                    borderWidth: 2,
                    borderColor: 'common.white',
                    bgcolor: varAlpha(theme.vars.palette.common.whiteChannel, 0.1),
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Báo giá tự động
              </Button>
            </Stack>
          </m.div>

          {/* Contact Info */}
          <m.div variants={varFade().inUp}>
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={4}
              alignItems="center"
              divider={
                <Box
                  sx={{
                    width: 1,
                    height: 20,
                    bgcolor: varAlpha(theme.vars.palette.common.whiteChannel, 0.3),
                    display: { xs: 'none', sm: 'block' },
                  }}
                />
              }
              sx={{
                mt: 4,
                pt: 4,
                borderTop: `1px solid ${varAlpha(theme.vars.palette.common.whiteChannel, 0.2)}`,
              }}
            >
              <Stack direction="row" spacing={1} alignItems="center">
                <Iconify icon="solar:phone-bold" sx={{ width: 20, height: 20 }} />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Hotline: 0123 456 789
                </Typography>
              </Stack>

              <Stack direction="row" spacing={1} alignItems="center">
                <Iconify icon="solar:chat-round-dots-bold" sx={{ width: 20, height: 20 }} />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Chat trực tuyến 24/7
                </Typography>
              </Stack>

              <Stack direction="row" spacing={1} alignItems="center">
                <Iconify icon="solar:clock-circle-bold" sx={{ width: 20, height: 20 }} />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Phản hồi trong 30 phút
                </Typography>
              </Stack>
            </Stack>
          </m.div>
        </Stack>
      </Container>
    </Box>
  );
}
