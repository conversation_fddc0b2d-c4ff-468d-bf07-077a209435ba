'use client';

import { m } from 'framer-motion';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { alpha, useTheme } from '@mui/material/styles';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { varAlpha } from 'minimal-shared/utils';

import { Iconify } from 'src/components/iconify';
import { varFade, MotionViewport } from 'src/components/animate';

// ----------------------------------------------------------------------

export function LomaHero() {
  const theme = useTheme();

  const renderContent = () => (
    <Container
      component={MotionViewport}
      sx={{
        py: { xs: 10, md: 15 },
        position: 'relative',
        zIndex: 2,
      }}
    >
      <Stack
        spacing={5}
        alignItems="center"
        sx={{
          textAlign: 'center',
          maxWidth: 800,
          mx: 'auto',
        }}
      >
        {/* Badge */}
        <m.div variants={varFade().inUp}>
          <Box
            sx={{
              px: 2,
              py: 0.5,
              borderRadius: 2,
              bgcolor: varAlpha(theme.vars.palette.primary.mainChannel, 0.1),
              border: `1px solid ${varAlpha(theme.vars.palette.primary.mainChannel, 0.2)}`,
              display: 'inline-flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Iconify icon="solar:verified-check-bold" sx={{ color: 'primary.main' }} />
            <Typography variant="caption" sx={{ color: 'primary.main', fontWeight: 600 }}>
              Đảm bảo chất lượng 100%
            </Typography>
          </Box>
        </m.div>

        {/* Main Headline */}
        <Stack spacing={3}>
          <m.div variants={varFade().inUp}>
            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: 40, md: 64 },
                fontWeight: 800,
                lineHeight: 1.1,
                background: `linear-gradient(135deg, ${theme.vars.palette.text.primary} 0%, ${theme.vars.palette.primary.main} 100%)`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Biến logo của bạn thành
              <br />
              <Box component="span" sx={{ color: 'primary.main' }}>
                công cụ marketing
              </Box>
              <br />
              mạnh mẽ
            </Typography>
          </m.div>

          <m.div variants={varFade().inUp}>
            <Typography
              variant="h5"
              sx={{
                color: 'text.secondary',
                fontWeight: 400,
                maxWidth: 600,
                mx: 'auto',
                lineHeight: 1.6,
              }}
            >
              Sản xuất túi vải premium với logo thương hiệu của bạn.
              Đảm bảo 1000 chiếc túi đồng nhất như 1 với quy trình kiểm soát chất lượng 6 bước.
            </Typography>
          </m.div>
        </Stack>

        {/* CTA Buttons */}
        <m.div variants={varFade().inUp}>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            justifyContent="center"
          >
            <Button
              component={RouterLink}
              href={paths.mockup}
              variant="contained"
              size="large"
              startIcon={<Iconify icon="solar:palette-2-bold" />}
              sx={{
                py: 1.5,
                px: 4,
                fontSize: 16,
                fontWeight: 600,
                borderRadius: 2,
                boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.3)}`,
                '&:hover': {
                  boxShadow: `0 12px 40px ${alpha(theme.palette.primary.main, 0.4)}`,
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s ease',
              }}
            >
              Tạo mockup miễn phí
            </Button>

            <Button
              component={RouterLink}
              href={paths.quote}
              variant="outlined"
              size="large"
              startIcon={<Iconify icon="solar:calculator-bold" />}
              sx={{
                py: 1.5,
                px: 4,
                fontSize: 16,
                fontWeight: 600,
                borderRadius: 2,
                borderWidth: 2,
                '&:hover': {
                  borderWidth: 2,
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s ease',
              }}
            >
              Báo giá tự động
            </Button>
          </Stack>
        </m.div>

        {/* Stats */}
        <m.div variants={varFade().inUp}>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={4}
            divider={
              <Box
                sx={{
                  width: 1,
                  height: 40,
                  bgcolor: 'divider',
                  display: { xs: 'none', sm: 'block' },
                }}
              />
            }
            sx={{ mt: 5 }}
          >
            {[
              { number: '10,000+', label: 'Túi đã sản xuất' },
              { number: '500+', label: 'Khách hàng tin tưởng' },
              { number: '99%', label: 'Hài lòng' },
            ].map((stat, index) => (
              <Stack key={index} spacing={0.5} alignItems="center">
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                  {stat.number}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {stat.label}
                </Typography>
              </Stack>
            ))}
          </Stack>
        </m.div>
      </Stack>
    </Container>
  );

  const renderBackground = () => (
    <Box
      sx={{
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        position: 'absolute',
        background: `linear-gradient(135deg,
          ${varAlpha(theme.vars.palette.primary.lighterChannel, 0.1)} 0%,
          ${varAlpha(theme.vars.palette.background.defaultChannel, 1)} 50%,
          ${varAlpha(theme.vars.palette.secondary.lighterChannel, 0.1)} 100%)`,
      }}
    />
  );

  return (
    <Box
      sx={{
        minHeight: '100vh',
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        overflow: 'hidden',
      }}
    >
      {renderBackground()}
      {renderContent()}
    </Box>
  );
}
