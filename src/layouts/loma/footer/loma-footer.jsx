'use client';

import { varAlpha } from 'minimal-shared/utils';

import Box from '@mui/material/Box';
import { Grid } from '@mui/material';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { Logo } from 'src/components/logo';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

const LINKS = [
  {
    headline: 'Sản phẩm',
    children: [
      { name: 'Túi vải canvas', href: paths.products },
      { name: 'Túi vải cotton', href: paths.products },
      { name: 'T<PERSON>i không dệt', href: paths.products },
      { name: 'T<PERSON>i giấy kraft', href: paths.products },
    ],
  },
  {
    headline: 'Dịch vụ',
    children: [
      { name: 'In logo', href: paths.mockup },
      { name: 'Thiết kế mockup', href: paths.mockup },
      { name: 'Báo giá tự động', href: paths.quote },
      { name: 'Tư vấn chuyên nghiệp', href: paths.contact },
    ],
  },
  {
    headline: 'Công ty',
    children: [
      { name: 'Về Loma', href: paths.about },
      { name: 'Quy trình sản xuất', href: paths.process },
      { name: 'Chất liệu & công nghệ', href: paths.materials },
      { name: 'Portfolio', href: paths.portfolio },
    ],
  },
  {
    headline: 'Hỗ trợ',
    children: [
      { name: 'Liên hệ', href: paths.contact },
      { name: 'FAQ', href: paths.faqs },
      { name: 'Blog', href: paths.blog },
      { name: 'Tài khoản', href: paths.account },
    ],
  },
];

const SOCIALS = [
  {
    name: 'Facebook',
    icon: 'eva:facebook-fill',
    href: '#',
  },
  {
    name: 'Instagram',
    icon: 'ant-design:instagram-filled',
    href: '#',
  },
  {
    name: 'LinkedIn',
    icon: 'eva:linkedin-fill',
    href: '#',
  },
  {
    name: 'YouTube',
    icon: 'eva:youtube-fill',
    href: '#',
  },
];

// ----------------------------------------------------------------------

export function LomaFooter({ sx, ...other }) {
  return (
    <Box
      component="footer"
      sx={[
        (theme) => ({
          py: 5,
          bgcolor: 'grey.900',
          color: 'common.white',
          position: 'relative',
          '&::before': {
            top: 0,
            left: 0,
            right: 0,
            height: 1,
            zIndex: 1,
            content: '""',
            position: 'absolute',
            background: `linear-gradient(90deg, transparent, ${varAlpha(theme.vars.palette.grey['500Channel'], 0.48)}, transparent)`,
          },
        }),
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      <Container>
        <Grid container spacing={3}>
          {/* Company Info */}
          <Grid size={{xs: 12, md: 4 }}>
            <Stack spacing={3}>
              <Logo sx={{ color: 'common.white' }} />
              
              <Typography variant="body2" sx={{ color: 'grey.500', maxWidth: 280 }}>
                Xưởng sản xuất túi vải chuyên nghiệp, biến logo của bạn thành công cụ marketing mạnh mẽ với chất lượng đồng nhất và dịch vụ tận tâm.
              </Typography>

              <Stack direction="row" spacing={1}>
                {SOCIALS.map((social) => (
                  <IconButton
                    key={social.name}
                    href={social.href}
                    sx={{
                      color: 'grey.500',
                      '&:hover': {
                        color: 'primary.main',
                      },
                    }}
                  >
                    <Iconify icon={social.icon} />
                  </IconButton>
                ))}
              </Stack>
            </Stack>
          </Grid>

          {/* Links */}
          <Grid size={{xs: 12, md: 8 }}>
            <Grid container spacing={3}>
              {LINKS.map((list) => (
                <Grid key={list.headline} size={{xs: 6, md: 3 }}>
                  <Typography variant="h6" sx={{ mb: 2, color: 'common.white' }}>
                    {list.headline}
                  </Typography>
                  
                  <Stack spacing={1}>
                    {list.children.map((link) => (
                      <Link
                        key={link.name}
                        component={RouterLink}
                        href={link.href}
                        color="grey.500"
                        variant="body2"
                        sx={{
                          '&:hover': {
                            color: 'primary.main',
                          },
                        }}
                      >
                        {link.name}
                      </Link>
                    ))}
                  </Stack>
                </Grid>
              ))}
            </Grid>
          </Grid>
        </Grid>

        <Divider sx={{ my: 3, borderColor: 'grey.800' }} />

        {/* Bottom */}
        <Stack
          direction={{ xs: 'column', md: 'row' }}
          spacing={2}
          justifyContent="space-between"
          alignItems="center"
        >
          <Typography variant="body2" sx={{ color: 'grey.500' }}>
            © 2024 Loma Bags. Tất cả quyền được bảo lưu.
          </Typography>
          
          <Stack direction="row" spacing={3}>
            <Link
              component={RouterLink}
              href="#"
              color="grey.500"
              variant="body2"
              sx={{ '&:hover': { color: 'primary.main' } }}
            >
              Chính sách bảo mật
            </Link>
            <Link
              component={RouterLink}
              href="#"
              color="grey.500"
              variant="body2"
              sx={{ '&:hover': { color: 'primary.main' } }}
            >
              Điều khoản sử dụng
            </Link>
          </Stack>
        </Stack>
      </Container>
    </Box>
  );
}
