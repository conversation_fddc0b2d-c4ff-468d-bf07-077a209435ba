'use client';

import { useState } from 'react';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import AppBar from '@mui/material/AppBar';
import Button from '@mui/material/Button';
import Toolbar from '@mui/material/Toolbar';
import Container from '@mui/material/Container';
import IconButton from '@mui/material/IconButton';
import { useTheme } from '@mui/material/styles';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { varAlpha } from 'minimal-shared/utils';

import { Iconify } from 'src/components/iconify';
import { Logo } from 'src/components/logo';

import { LomaNavMobile } from './loma-nav-mobile';
import { LomaNavDesktop } from './loma-nav-desktop';

// ----------------------------------------------------------------------

const NAV_ITEMS = [
  { title: 'Trang chủ', path: paths.home },
  { title: 'Sản phẩm', path: paths.products },
  { title: 'Mockup Studio', path: paths.mockup },
  { title: 'Quy trình', path: paths.process },
  { title: 'Chất liệu', path: paths.materials },
  { title: 'Về chúng tôi', path: paths.about },
  { title: 'Liên hệ', path: paths.contact },
];

// ----------------------------------------------------------------------

export function LomaHeader({ sx, ...other }) {
  const theme = useTheme();
  const [openMobile, setOpenMobile] = useState(false);

  const handleOpenMobile = () => setOpenMobile(true);
  const handleCloseMobile = () => setOpenMobile(false);

  return (
    <>
      <AppBar
        position="fixed"
        sx={[
          {
            bgcolor: varAlpha(theme.vars.palette.background.defaultChannel, 0.9),
            backdropFilter: 'blur(16px)',
            borderBottom: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.12)}`,
            boxShadow: 'none',
          },
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
        {...other}
      >
        <Container maxWidth="xl">
          <Toolbar
            disableGutters
            sx={{
              height: { xs: 64, md: 80 },
              transition: theme.transitions.create(['height'], {
                easing: theme.transitions.easing.easeInOut,
                duration: theme.transitions.duration.shorter,
              }),
            }}
          >
            {/* Logo */}
            <Logo
              component={RouterLink}
              href={paths.home}
              sx={{ mr: 2.5 }}
            />

            {/* Desktop Navigation */}
            <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' } }}>
              <LomaNavDesktop data={NAV_ITEMS} />
            </Box>

            {/* CTA Buttons */}
            <Stack direction="row" spacing={1} alignItems="center">
              <Button
                component={RouterLink}
                href={paths.quote}
                variant="outlined"
                color="primary"
                size="small"
                sx={{ display: { xs: 'none', sm: 'inline-flex' } }}
              >
                Báo giá
              </Button>
              
              <Button
                component={RouterLink}
                href={paths.mockup}
                variant="contained"
                color="primary"
                size="small"
                startIcon={<Iconify icon="solar:palette-2-bold" />}
                sx={{ display: { xs: 'none', sm: 'inline-flex' } }}
              >
                Tạo mockup
              </Button>

              {/* Mobile Menu Button */}
              <IconButton
                onClick={handleOpenMobile}
                sx={{ 
                  display: { md: 'none' },
                  color: 'text.primary',
                }}
              >
                <Iconify icon="solar:hamburger-menu-bold" />
              </IconButton>
            </Stack>
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Navigation */}
      <LomaNavMobile
        data={NAV_ITEMS}
        open={openMobile}
        onClose={handleCloseMobile}
      />
    </>
  );
}
